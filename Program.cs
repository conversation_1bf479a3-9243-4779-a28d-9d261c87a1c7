using System;

namespace SimpleApp
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("欢迎使用简单的C#程序！");
            Console.WriteLine("======================");
            
            // 获取用户输入
            Console.Write("请输入您的姓名: ");
            string? name = Console.ReadLine();
            
            if (!string.IsNullOrEmpty(name))
            {
                Console.WriteLine($"你好, {name}!");
                Console.WriteLine($"今天是: {DateTime.Now:yyyy年MM月dd日 HH:mm:ss}");
            }
            else
            {
                Console.WriteLine("你好, 匿名用户!");
            }
            
            // 简单的计算器功能
            Console.WriteLine("\n简单计算器:");
            Console.Write("请输入第一个数字: ");
            if (double.TryParse(Console.ReadLine(), out double num1))
            {
                Console.Write("请输入第二个数字: ");
                if (double.TryParse(Console.ReadLine(), out double num2))
                {
                    Console.WriteLine($"加法: {num1} + {num2} = {num1 + num2}");
                    Console.WriteLine($"减法: {num1} - {num2} = {num1 - num2}");
                    Console.WriteLine($"乘法: {num1} × {num2} = {num1 * num2}");
                    if (num2 != 0)
                    {
                        Console.WriteLine($"除法: {num1} ÷ {num2} = {num1 / num2:F2}");
                    }
                    else
                    {
                        Console.WriteLine("除法: 不能除以零!");
                    }
                }
                else
                {
                    Console.WriteLine("第二个数字输入无效!");
                }
            }
            else
            {
                Console.WriteLine("第一个数字输入无效!");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
